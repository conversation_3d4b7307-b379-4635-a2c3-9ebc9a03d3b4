<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <!-- NuGet Package Configuration -->
        <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
        <PackageId>VirtuManager.Auth.Grpc</PackageId>
        <PackageVersion>1.0.0</PackageVersion>
        <Authors>VirtuManager Team</Authors>
        <Company>VirtuManager</Company>
        <Product>VirtuManager Auth Service</Product>
        <Description>gRPC contracts and generated code for VirtuManager Auth Service</Description>
        <PackageTags>grpc;protobuf;auth;virtumanager</PackageTags>
        <RepositoryUrl>https://github.com/your-org/virtu-manager-auth-service</RepositoryUrl>
        <PackageLicenseExpression>MIT</PackageLicenseExpression>
        <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>
        <IncludeSymbols>true</IncludeSymbols>
        <SymbolPackageFormat>snupkg</SymbolPackageFormat>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Google.Protobuf" Version="3.31.1" />
      <PackageReference Include="Grpc.AspNetCore" Version="2.71.0" />
      <PackageReference Include="Grpc.Tools" Version="2.72.0">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <Protobuf Include="Protos\**\*.proto" GrpcServices="Both" />
    </ItemGroup>
    
</Project>
