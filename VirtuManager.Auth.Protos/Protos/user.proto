syntax = "proto3";

import "google/protobuf/empty.proto";
//import "google/protobuf/timestamp.proto";

option csharp_namespace = "VirtuManager.Auth.Grpc";

package User;

service GrpcUserService {
  rpc GetUserByGuid(GetUserByGuidRequest) returns (User);
  rpc GetUserByLogin(GetUserByLoginRequest) returns (User);
  rpc GetUsersPaged(GetUsersPagedRequest) returns (GetUsersPagedResponse);
  rpc GetAllUsers(google.protobuf.Empty) returns(GetUsersResponse);
}

message GetUserByGuidRequest {
  string guid = 1;
}

message GetUserByLoginRequest {
  string login = 1;
}

message GetUsersPagedRequest {
  int32 pageNumber = 1;
  int32 pageSize = 2;
}

message GetUsersResponse {
  repeated User users = 1;
}

message GetUsersPagedResponse {
  int64 totalCount = 1;
  int32 pageNumber = 2;
  int32 pageSize = 3;
  double totalPages = 4;
  repeated User users = 5;
}

message User {
  string guid = 1;
  string login = 2;
  string email = 3;
  UserRole role = 4;
  //  google.protobuf.Timestamp createdAt = 5;
  //  google.protobuf.Timestamp updatedAt = 6;
  //  google.protobuf.Timestamp lastLoginAt = 7;
}

enum UserRole {
  Regular = 0;
  Developer = 1;
  Admin = 2;
}
