// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: user.proto
// </auto-generated>
#pragma warning disable 0414, 1591, 8981, 0612
#region Designer generated code

using grpc = global::Grpc.Core;

namespace VirtuManager.Auth.Grpc {
  public static partial class GrpcUserService
  {
    static readonly string __ServiceName = "User.GrpcUserService";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VirtuManager.Auth.Grpc.GetUserByGuidRequest> __Marshaller_User_GetUserByGuidRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VirtuManager.Auth.Grpc.GetUserByGuidRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VirtuManager.Auth.Grpc.User> __Marshaller_User_User = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VirtuManager.Auth.Grpc.User.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VirtuManager.Auth.Grpc.GetUserByLoginRequest> __Marshaller_User_GetUserByLoginRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VirtuManager.Auth.Grpc.GetUserByLoginRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VirtuManager.Auth.Grpc.GetUsersPagedRequest> __Marshaller_User_GetUsersPagedRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VirtuManager.Auth.Grpc.GetUsersPagedRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VirtuManager.Auth.Grpc.GetUsersPagedResponse> __Marshaller_User_GetUsersPagedResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VirtuManager.Auth.Grpc.GetUsersPagedResponse.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::Google.Protobuf.WellKnownTypes.Empty> __Marshaller_google_protobuf_Empty = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::Google.Protobuf.WellKnownTypes.Empty.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::VirtuManager.Auth.Grpc.GetUsersResponse> __Marshaller_User_GetUsersResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::VirtuManager.Auth.Grpc.GetUsersResponse.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::VirtuManager.Auth.Grpc.GetUserByGuidRequest, global::VirtuManager.Auth.Grpc.User> __Method_GetUserByGuid = new grpc::Method<global::VirtuManager.Auth.Grpc.GetUserByGuidRequest, global::VirtuManager.Auth.Grpc.User>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetUserByGuid",
        __Marshaller_User_GetUserByGuidRequest,
        __Marshaller_User_User);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::VirtuManager.Auth.Grpc.GetUserByLoginRequest, global::VirtuManager.Auth.Grpc.User> __Method_GetUserByLogin = new grpc::Method<global::VirtuManager.Auth.Grpc.GetUserByLoginRequest, global::VirtuManager.Auth.Grpc.User>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetUserByLogin",
        __Marshaller_User_GetUserByLoginRequest,
        __Marshaller_User_User);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::VirtuManager.Auth.Grpc.GetUsersPagedRequest, global::VirtuManager.Auth.Grpc.GetUsersPagedResponse> __Method_GetUsersPaged = new grpc::Method<global::VirtuManager.Auth.Grpc.GetUsersPagedRequest, global::VirtuManager.Auth.Grpc.GetUsersPagedResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetUsersPaged",
        __Marshaller_User_GetUsersPagedRequest,
        __Marshaller_User_GetUsersPagedResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::Google.Protobuf.WellKnownTypes.Empty, global::VirtuManager.Auth.Grpc.GetUsersResponse> __Method_GetAllUsers = new grpc::Method<global::Google.Protobuf.WellKnownTypes.Empty, global::VirtuManager.Auth.Grpc.GetUsersResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetAllUsers",
        __Marshaller_google_protobuf_Empty,
        __Marshaller_User_GetUsersResponse);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::VirtuManager.Auth.Grpc.UserReflection.Descriptor.Services[0]; }
    }

    /// <summary>Base class for server-side implementations of GrpcUserService</summary>
    [grpc::BindServiceMethod(typeof(GrpcUserService), "BindService")]
    public abstract partial class GrpcUserServiceBase
    {
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::VirtuManager.Auth.Grpc.User> GetUserByGuid(global::VirtuManager.Auth.Grpc.GetUserByGuidRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::VirtuManager.Auth.Grpc.User> GetUserByLogin(global::VirtuManager.Auth.Grpc.GetUserByLoginRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::VirtuManager.Auth.Grpc.GetUsersPagedResponse> GetUsersPaged(global::VirtuManager.Auth.Grpc.GetUsersPagedRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::VirtuManager.Auth.Grpc.GetUsersResponse> GetAllUsers(global::Google.Protobuf.WellKnownTypes.Empty request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Client for GrpcUserService</summary>
    public partial class GrpcUserServiceClient : grpc::ClientBase<GrpcUserServiceClient>
    {
      /// <summary>Creates a new client for GrpcUserService</summary>
      /// <param name="channel">The channel to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public GrpcUserServiceClient(grpc::ChannelBase channel) : base(channel)
      {
      }
      /// <summary>Creates a new client for GrpcUserService that uses a custom <c>CallInvoker</c>.</summary>
      /// <param name="callInvoker">The callInvoker to use to make remote calls.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public GrpcUserServiceClient(grpc::CallInvoker callInvoker) : base(callInvoker)
      {
      }
      /// <summary>Protected parameterless constructor to allow creation of test doubles.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected GrpcUserServiceClient() : base()
      {
      }
      /// <summary>Protected constructor to allow creation of configured clients.</summary>
      /// <param name="configuration">The client configuration.</param>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected GrpcUserServiceClient(ClientBaseConfiguration configuration) : base(configuration)
      {
      }

      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Grpc.User GetUserByGuid(global::VirtuManager.Auth.Grpc.GetUserByGuidRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUserByGuid(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Grpc.User GetUserByGuid(global::VirtuManager.Auth.Grpc.GetUserByGuidRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetUserByGuid, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Grpc.User> GetUserByGuidAsync(global::VirtuManager.Auth.Grpc.GetUserByGuidRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUserByGuidAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Grpc.User> GetUserByGuidAsync(global::VirtuManager.Auth.Grpc.GetUserByGuidRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetUserByGuid, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Grpc.User GetUserByLogin(global::VirtuManager.Auth.Grpc.GetUserByLoginRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUserByLogin(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Grpc.User GetUserByLogin(global::VirtuManager.Auth.Grpc.GetUserByLoginRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetUserByLogin, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Grpc.User> GetUserByLoginAsync(global::VirtuManager.Auth.Grpc.GetUserByLoginRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUserByLoginAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Grpc.User> GetUserByLoginAsync(global::VirtuManager.Auth.Grpc.GetUserByLoginRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetUserByLogin, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Grpc.GetUsersPagedResponse GetUsersPaged(global::VirtuManager.Auth.Grpc.GetUsersPagedRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUsersPaged(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Grpc.GetUsersPagedResponse GetUsersPaged(global::VirtuManager.Auth.Grpc.GetUsersPagedRequest request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetUsersPaged, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Grpc.GetUsersPagedResponse> GetUsersPagedAsync(global::VirtuManager.Auth.Grpc.GetUsersPagedRequest request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetUsersPagedAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Grpc.GetUsersPagedResponse> GetUsersPagedAsync(global::VirtuManager.Auth.Grpc.GetUsersPagedRequest request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetUsersPaged, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Grpc.GetUsersResponse GetAllUsers(global::Google.Protobuf.WellKnownTypes.Empty request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetAllUsers(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::VirtuManager.Auth.Grpc.GetUsersResponse GetAllUsers(global::Google.Protobuf.WellKnownTypes.Empty request, grpc::CallOptions options)
      {
        return CallInvoker.BlockingUnaryCall(__Method_GetAllUsers, null, options, request);
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Grpc.GetUsersResponse> GetAllUsersAsync(global::Google.Protobuf.WellKnownTypes.Empty request, grpc::Metadata headers = null, global::System.DateTime? deadline = null, global::System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
      {
        return GetAllUsersAsync(request, new grpc::CallOptions(headers, deadline, cancellationToken));
      }
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual grpc::AsyncUnaryCall<global::VirtuManager.Auth.Grpc.GetUsersResponse> GetAllUsersAsync(global::Google.Protobuf.WellKnownTypes.Empty request, grpc::CallOptions options)
      {
        return CallInvoker.AsyncUnaryCall(__Method_GetAllUsers, null, options, request);
      }
      /// <summary>Creates a new instance of client from given <c>ClientBaseConfiguration</c>.</summary>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      protected override GrpcUserServiceClient NewInstance(ClientBaseConfiguration configuration)
      {
        return new GrpcUserServiceClient(configuration);
      }
    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(GrpcUserServiceBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_GetUserByGuid, serviceImpl.GetUserByGuid)
          .AddMethod(__Method_GetUserByLogin, serviceImpl.GetUserByLogin)
          .AddMethod(__Method_GetUsersPaged, serviceImpl.GetUsersPaged)
          .AddMethod(__Method_GetAllUsers, serviceImpl.GetAllUsers).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, GrpcUserServiceBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_GetUserByGuid, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::VirtuManager.Auth.Grpc.GetUserByGuidRequest, global::VirtuManager.Auth.Grpc.User>(serviceImpl.GetUserByGuid));
      serviceBinder.AddMethod(__Method_GetUserByLogin, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::VirtuManager.Auth.Grpc.GetUserByLoginRequest, global::VirtuManager.Auth.Grpc.User>(serviceImpl.GetUserByLogin));
      serviceBinder.AddMethod(__Method_GetUsersPaged, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::VirtuManager.Auth.Grpc.GetUsersPagedRequest, global::VirtuManager.Auth.Grpc.GetUsersPagedResponse>(serviceImpl.GetUsersPaged));
      serviceBinder.AddMethod(__Method_GetAllUsers, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::Google.Protobuf.WellKnownTypes.Empty, global::VirtuManager.Auth.Grpc.GetUsersResponse>(serviceImpl.GetAllUsers));
    }

  }
}
#endregion
