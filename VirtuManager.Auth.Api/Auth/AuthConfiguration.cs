using System.Text;
using Microsoft.IdentityModel.Tokens;

namespace virtu_manager_auth.Auth;

public class AuthConfiguration
{
    private readonly AuthOptions _authOptions;
    
    public AuthConfiguration(AuthOptions options)
    {
        _authOptions = options;
    }
    
    public string Audience => _authOptions.Audience!;
    
    public string Issuer => _authOptions.Issuer!;
    
    public SymmetricSecurityKey GetSymmetricSecurityKey()
        => new (Encoding.UTF8.GetBytes(_authOptions.Key!));
    
    public int AccessTokenLifetime => _authOptions.AccessTokenLifetime;
    
    public int RefreshTokenLifetime => _authOptions.RefreshTokenLifetime;
}