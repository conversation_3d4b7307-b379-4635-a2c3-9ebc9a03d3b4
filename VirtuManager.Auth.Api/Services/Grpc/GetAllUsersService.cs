using Google.Protobuf.WellKnownTypes;
using Grpc.Core;
using Microsoft.AspNetCore.Authorization;
using virtu_manager_auth.Mapping;
using VirtuManager.Auth.Api.Services.Interfaces;

namespace virtu_manager_auth.Services.Grpc;

[Authorize]
public class GetAllUsersService : GrpcUserService.GrpcUserServiceBase
{
    private readonly IGetUsersService _getUsersService;
    
    public GetAllUsersService(IGetUsersService getUsersService)
    {
        ArgumentNullException.ThrowIfNull(getUsersService, nameof(getUsersService));
        _getUsersService = getUsersService;
    }

    public override async Task<GetUsersResponse> GetAllUsers(Empty request, ServerCallContext context)
    {
        var users = await _getUsersService.GetAll();
        
        var response = new GetUsersResponse();
        response.Users.AddRange(users.Select(u => u.ToGrpcModel()));
        
        return response;
    }
}